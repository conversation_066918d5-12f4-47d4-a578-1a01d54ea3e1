.jt-temu-product-box {
  padding: 10px;
  background: linear-gradient(325deg, #e6f3ff, white);
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 4px 6px 24px 0 rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.jt-temu-product-box .error {
  padding: 10px 0 30px;
  width: 100%;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.jt-temu-product-box .error .jtyt-temu-down {
  font-size: 14px;
  color: #fff;
  margin-top: 10px;
  text-align: center;
  cursor: pointer;
  padding: 6px 15px;
  background: #439eff;
  border-radius: 3px;
  transition: all 0.2s;
}

.jt-temu-product-box .error .jtyt-temu-down:hover {
  opacity: 0.8;
}

.jt-temu-product-box .logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.jt-temu-product-box .tips {
  padding: 4px 10px;
  background: rgb(232, 243, 255);
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 12px;
  width: max-content;
}

.jt-temu-product-box .tips span.icon {
  display: flex;
}

.jt-temu-product-box .tips span.txt {
  font-size: 12px;
  color: #333;
}

.jt-temu-product-box .logo img {
  width: 100px;
}

.jt-temu-product-box .logo .updatetime {
  font-size: 14px;
  color: #666;
}

.jt-temu-product-box .logo .updatebtn {
  font-size: 12px !important;
  color: #fff;
  background-color: #eee;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.jt-temu-product-box .logo .updatebtn .refreshing-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.jt-temu-product-box .logo .updateCjbtn {
  padding: 3px 6px;
  border-radius: 4px;
  background: #439eff;
  color: #fff;
  font-size: 12px;
  transition: all 0.2s;
  cursor: pointer;
}

.jt-temu-product-box .logo .updateCjbtn:hover {
  opacity: 0.8;
}

.jt-temu-product-box .logo .updateCjbtn .updateCjbtn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.jt-temu-product-box .logo .updatebtn .isUpdating-icon,
#todayAdsAllSpend .updatebtn .isUpdating-icon
{
  border: 2px solid transparent;
  border-top: 2px solid #439eff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
  display: none;
}

.jt-temu-product-box .logo .updatebtn:hover,
.jt-temu-product-box .logo .displayDataBtn:hover,
#todayAdsAllSpend .updatebtn:hover
{
  opacity: 0.8;
}

#todayAdsAllSpend .lookTodayAdsDetailBtn {
  transition: all 0.2s;
  cursor: pointer;
}
#todayAdsAllSpend .lookTodayAdsDetailBtn:hover {
  opacity: 0.8;
}

.jt-temu-product-box .logo .displayDataBtn {
  font-size: 12px !important;
  color: #fff;
  background-color: #eee;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.jt-temu-product-box .logo .displayDataBtn .displayData-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.jt-temu-product-box .logo .displayDataBtn .hideData-icon {
  align-items: center;
  justify-content: center;
  display: none;
}

.jt-temu-product-box .product-data-content {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.jt-temu-product-box .product-data-content .sales {
  display: flex;
  gap: 12px;
  padding: 8px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 0 10px #00000005;
  height: 62px;
}

.jt-temu-product-box .product-data-content .sales .con {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  min-width: max-content;
  position: relative;
}

.vip-icon-box {
  position: absolute;
  top: -5px;
  right: -6px;
}

.jt-temu-product-box .product-data-content .box {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  padding: 8px;
  gap: 10px;
  background: #fff;
  border-radius: 12px;
  min-width: max-content;
  box-shadow: 0 0 10px #00000005;
  height: 62px;
}

/* 滚动条宽度 */
.jt-temu-product-box .product-data-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滚动条轨道 */
.jt-temu-product-box .product-data-content::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 */
.jt-temu-product-box .product-data-content::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

/* 鼠标悬停在滚动条滑块上 */
.jt-temu-product-box .product-data-content::-webkit-scrollbar-thumb:hover {
  background: #c9c9c9;
  cursor: pointer;
}

.jt-temu-product-box .product-data-content .box span.title {
  font-size: 14px;
  line-height: 1;
  color: #00000099;
}

.jt-temu-product-box .product-data-content .sales .con span.title,
.jt-temu-product-box .product-data-content .con span.title {
  font-size: 14px;
  color: #00000099;
  display: flex;
  align-items: center;
  gap: 5px;
}

.jt-temu-product-box .product-data-content .sales .con span.title .outputExcelBtn,
.jt-temu-product-box .product-data-content .con span.title .stockDetails,
.jt-temu-product-box .product-data-content .con span.title .stockPriceDetails,
.jt-temu-product-box .product-data-content .con span.title .slowMovingInventoryValueAtRiskBtn,
.jt-temu-product-box .product-data-content .con span.title .slowMovingInventoryValueBtn,
.jt-temu-product-box .product-data-content .con span.title .productRateReturnBtn,
.jt-temu-product-box .product-data-content .con span.title .stockPriceDetailsWaitBtn {
  font-size: 12px !important;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.jt-temu-product-box .product-data-content #productRateReturnBtn,
.jt-temu-product-box .product-data-content #temuSellerDataOverviewBtn,
.jt-temu-product-box .product-data-content #chooseDataSaleDetailBtn,
.jt-temu-product-box .product-data-content #setProductsCostBtn,
.jt-temu-product-box .product-data-content #chooseDataSaleDetailBtn1,
.jt-temu-product-box .product-data-content #refuseAdjustPriceByCustomDataBtn,
.jt-temu-product-box .product-data-content #getFundPriceByCustomDataBtn
.jt-temu-product-box .product-data-content #searchStockListBtn,
.jt-temu-product-box.product-data-content #chooseDataAdsDetailBtn
{
  cursor: pointer !important;
}

.jt-temu-product-box .product-data-content .box span.num,
.jt-temu-product-box .product-data-content .sales .con span.num {
  font-size: 18px;
  color: #3d3d3d;
  font-weight: 500;
}

.jt-temu-product-box .product-data-content .box .profitBar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.jt-temu-product-box .product-data-content .box .profitBar.estimatedProfit,
.jt-temu-product-box .product-data-content .box .profitBar.estimatedProfit30 .jt-temu-product-box .product-data-content .box .profitBar.estimatedProfit30_1 {
  font-size: 18px;
  color: rgb(196 150 49 / 80%);
  font-weight: 500;
  display: none;
}

.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn,
.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn30,
.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn7,
.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn30_1,
.customSalesBtn,
.chooseDataSaleDetailBtn,
.temuSellerDataOverviewBtn,
.setProductsCostBtn,
.refuseAdjustPriceByCustomData,
.searchStockListBtn
{
  font-size: 12px !important;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customText {
  display: flex;
  align-items: center;
  gap: 8px;
}

.outputExcelBtn {
  cursor: pointer;
  transition: all 0.3s;
}

.outputExcelBtn:hover {
  opacity: 0.8;
}

.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn:hover,
.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn30:hover,
.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn7:hover,
.jt-temu-product-box .product-data-content .box .profitBar .estimatedAfterSalesBtn30_1:hover,
.chooseDataSaleDetailBtn:hover,
.temuSellerDataOverviewBtn:hover,
.setProductsCostBtn:hover,
.refuseAdjustPriceByCustomData:hover,
.searchStockListBtn:hover
{
  opacity: 0.8;
}

.jt-temu-product-box .loading {
  flex-direction: column;
  gap: 8px;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.85);
  z-index: 99;
  display: none;
}

.jt-temu-product-box .loading .loader {
  border: 2px solid transparent;
  border-top: 2px solid #439eff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

.jt-temu-product-box .loading .text {
  font-size: 12px;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

#estimatedAfterSalesBtn30_1 .jtyt-loader,
#searchStockListBtn .jtyt-loader
{
  border: 2.5px solid transparent;
  border-top: 2.5px solid #439eff;
  border-radius: 50%;
  width: 21px;
  height: 21px;
  animation: spin 0.8s linear infinite;
  display: inline-block;
  display: none;
  position: relative;
}

#jtyt-cj-box .jt-sidebar .jt-content {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  z-index: 9999999;
}

#jtyt-cj-box .jt-sidebar .jt-content .jt-photo .jt-iframe,
#jtyt-cj-box .jt-sidebar .jt-content .jt-video .jt-iframe {
  width: 100%;
  height: 100vh;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list {
  position: absolute;
  left: -56px;
  padding: 12px 8px 10px 8px;
  background: linear-gradient(23deg, #e6f3ff, white);
  border-radius: 12px;
  box-shadow: 0 0 10px #0000001a;
  margin: 0 !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 9px;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item {
  width: 35px;
  height: 35px;
  position: relative;
  transition: all 0.3s;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(1) {
  cursor: auto !important;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(1):hover {
  opacity: unset !important;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(2) {
  height: 0 !important;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i:hover {
  background-color: #00000010;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i {
  display: block;
  width: 35px;
  height: 35px;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s;
  border-radius: 8px;
  cursor: pointer;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i.jt-photo {
  background-size: 23px 23px;
  background-image: url(data:image/png;base64,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);
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i.jt-video {
  background-size: 22px 20px;
  background-image: url(data:image/png;base64,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);
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i.jt-box {
  background-size: 27px 27px;
  background-image: url(data:image/png;base64,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);
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i.jt-toggle-mini {
  border-radius: 30px;
  background-size: 20px 20px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzI2MjM2MjQ3NTkzIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjExNDcxIiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmk0LjI0OTMzYTgxRFhWeFpGIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik04MjIuNDk5ODM2IDUyOC4xMjc4Nkw2MTkuOTI3NzUgOTg3LjI2MDQyN0E0My4wNDQ5OTIgNDMuMDQ0OTkyIDAgMSAxIDU0MS4xMzgwNDEgOTUyLjUzODU4OEw3MzYuMDU5NTUxIDUxMC43MjQ5MDQgNTQxLjEzODA0MSA2OC45ODEyODFhNDMuMDU5MDA0IDQzLjA1OTAwNCAwIDAgMSA3OC43NzU2OTctMzQuNzQ5ODYzTDgyMi41Mjc4NiA0OTMuMzYzOTg1YTQzLjA4NzAyOCA0My4wODcwMjggMCAwIDEgMCAzNC43NDk4NjN6IG0tMzM3LjYyMDE0MyAwTDI4Mi4zMDc2MDggOTg3LjI2MDQyN0E0My4wNDQ5OTIgNDMuMDQ0OTkyIDAgMSAxIDIwMy41MzE5MSA5NTIuNTM4NTg4TDM5OC40NTM0MjEgNTEwLjcyNDkwNGwtMTk0Ljg5MzQ4Ny00NDEuNzQzNjIzYTQzLjA0NDk5MiA0My4wNDQ5OTIgMCAxIDEgNzguNzc1Njk4LTM0Ljc0OTg2M0w0ODQuOTA3NzE4IDQ5My4zNjM5ODVhNDMuMDU5MDA0IDQzLjA1OTAwNCAwIDAgMSAwIDM0Ljc0OTg2M3ogbTAgMCIgZmlsbD0iIzcwNzA3MCIgcC1pZD0iMTE0NzIiPjwvcGF0aD48L3N2Zz4=);
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item span {
  position: absolute;
  left: -95px;
  top: 2px;
  width: 80px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  text-align: center;
  color: #fff;
  transition: all 0.3s;
  border-radius: 8px;
  display: none;
  background: rgba(0, 0, 0, 1);
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(1) span {
  position: absolute;
  left: -95px;
  top: 2px;
  width: 80px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  text-align: center;
  color: #fff;
  transition: all 0.3s;
  border-radius: 8px;
  display: none;
  background: #000;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(5) span {
  position: absolute;
  left: -95px;
  top: 2px;
  width: 80px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  text-align: center;
  color: #fff;
  transition: all 0.3s;
  border-radius: 8px;
  display: none;
  background: rgba(0, 0, 0, 1);
}

@-webkit-keyframes jt-fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes jt-fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

#jtyt-cj-box .jt-fadeIn {
  -webkit-animation-name: jt-fadeIn;
  animation-name: jt-fadeIn;
}

#jtyt-cj-box .jt-animated {
  -webkit-animation-duration: 0.4s;
  animation-duration: 0.4s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item i:hover~span {
  display: block;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(1) div:hover {
  cursor: pointer;
}

#jtyt-cj-box .jt-sidebar .jt-nav-list .jt-nav-item:nth-child(1) div:hover~span {
  display: block;
}

#jtyt-cj-box .jt-box {
  width: 100%;
  height: 100%;
}

#jtyt-cj-box .jt-iframe {
  width: 100%;
  height: 100%;
}

#jtyt-cj-box #jt-iframe-page {
  width: 100%;
  height: 100%;
  border: none;
  background: linear-gradient(0deg, #e6f3ff, white);
}

#jtyt-cj-box .jtyt-video-con {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

#jtyt-cj-box .jtyt-video-box {
  width: 200px;
  height: 200px;
  border: 1px solid #333;
}

#jtyt-cj-compliance-modal {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9999999 !important;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox {
  width: 600px;
  height: 362px;
  overflow: auto;
  position: absolute;
  top: calc(50% - 181px);
  left: calc(50% - 300px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
  box-shadow: 0 0 22px rgba(0, 0, 0, 0.25);
  padding: 5px 15px 15px;
  transition: all 0.2s;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox #jtyt-cj-compliance-titlebar {
  width: 100%;
  min-height: 40px;
  max-height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox #jtyt-cj-compliance-titlebar .jtyt-cj-compliance-btnArea {
  display: flex;
  align-items: center;
  gap: 15px;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox #jtyt-cj-compliance-titlebar #jtyt-cj-compliance-closebtn {
  cursor: pointer;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox #jtyt-cj-compliance-titlebar #jtyt-cj-compliance-closebtn:hover {
  color: #555;
  background: #eee;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox #jtyt-cj-compliance-titlebar #jtyt-cj-compliance-closebtn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border-radius: 3px;
}

.jtyt-cj-compliance-tabs-btns {
  display: flex;
  gap: 10px;
}

.jtyt-cj-compliance-tabs-btns>div {
  transition: all 0.3s;
}

.jtyt-cj-compliance-tabs-btns>div:hover {
  background: #f5f5f5 !important;
}

.jtyt-cj-compliance-tabs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.jtyt-cj-compliance-tabs-btn {
  padding: 4px 8px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.jtyt-cj-compliance-tabs-btn.btn-actived {
  border-bottom-color: #439eff !important;
}

.jtyt-cj-compliance-tabs-content {
  display: none;
  flex-direction: column;
  gap: 30px;
}

.jtyt-cj-compliance-tabs-content.content-actived {
  display: flex !important;
}

#jtyt-cj-compliance-modal #jtyt-cj-compliance-modalbox .jtyt-cj-compliance-modalcontent {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.jtyt-cj-compliance-modalcontent-tips {
  color: #f56c6c;
}

.jtyt-cj-compliance-btns {
  display: flex !important;
  justify-content: center !important;
  gap: 10px !important;
  align-items: center !important;
}

.jtyt-cj-compliance-btns-confirm1,
.jtyt-cj-compliance-btns-confirm2 {
  padding: 4px 20px;
  border-radius: 4px;
  cursor: pointer;
  background: #439eff;
  color: #fff;
  border: 1px solid #439eff;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.jtyt-cj-compliance-btns-confirm1 .jtyt-loader,
.jtyt-cj-compliance-btns-confirm2 .jtyt-loader {
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  animation: spin 1s linear infinite;
  display: none;
}

.jtyt-cj-compliance-checkbox {
  font-size: 14px;
  margin-top: 5px;
  display: flex;
  align-items: center;
}

.jtyt-cj-compliance-checkbox-name {
  margin-right: 10px;
}

#jtyt-cj-checkbox1 {
  margin-right: 3px;
  cursor: pointer;
}

#jtyt-cj-checkbox2 {
  margin-right: 3px;
  cursor: pointer;
}

.jtyt-cj-compliance-checkbox-label {
  cursor: pointer;
}

.jtyt-cj-compliance-btns-cancel1,
.jtyt-cj-compliance-btns-cancel2 {
  padding: 4px 20px;
  border-radius: 4px;
  border: 1px solid #999;
  cursor: pointer;
  transition: all 0.2s;
}

.jtyt-cj-compliance-btns-cancel1:hover,
.jtyt-cj-compliance-btns-confirm1:hover,
.jtyt-cj-compliance-btns-cancel2:hover,
.jtyt-cj-compliance-btns-confirm2:hover {
  opacity: 0.8;
}

.jtyt-cj-compliance-tips {
  color: #333;
  font-size: 16px;
}

.jtyt-cj-compliance-input1,
.jtyt-cj-compliance-input2 {
  margin-top: 10px;
  outline: none;
  border: 1.5px solid #ccc;
  border-radius: 3px;
  height: 36px;
  color: #333;
  padding: 4px 5px;
  box-sizing: border-box;
}

.jtyt-cj-compliance-input1[type="text"]:focus,
.jtyt-cj-compliance-input2[type="text"]:focus {
  border-color: rgba(102, 175, 233, 0.6);
  outline: none;
  box-shadow: 0 0 4px rgba(102, 175, 233, 0.4);
}

.jtyt-cj-compliance-input-tips {
  margin-top: 2px;
  color: #f56c6c;
  font-size: 14px;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

.animated1 {
  -webkit-animation-duration: 0.35s;
  animation-duration: 0.35s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@keyframes zoomOut {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 0;
  }
}

.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

.animated {
  -webkit-animation-duration: 0.35s;
  animation-duration: 0.35s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

#jtyt-DeliveryModal-notice-alert-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999999;
}

.jtyt-delivery-modal-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  width: 340px;
}

.jtyt-delivery-modal-tip-progress-bar {
  width: 60px;
  height: 60px;
  position: relative;
}

.jtyt-delivery-modal-tip-progress-bar .shape {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 40%;
  opacity: 0.7;
}

.jtyt-delivery-modal-tip-progress-bar .shape-1 {
  left: -40px;
  -webkit-animation: Animation8Shape1 1.5s ease infinite;
  animation: Animation8Shape1 1.5s ease infinite;
}

.jtyt-delivery-modal-tip-progress-bar .shape-2 {
  left: 0px;
  -webkit-animation: Animation8Shape2 1.5s ease infinite 0.5s;
  animation: Animation8Shape2 1.5s ease infinite 0.5s;
}

.jtyt-delivery-modal-tip-progress-bar .shape-3 {
  left: 40px;
  -webkit-animation: Animation8Shape3 1.5s ease infinite 0.8s;
  animation: Animation8Shape3 1.5s ease infinite 0.8s;
}

.jtyt-delivery-modal-tip-progress-bar .shape-4 {
  left: 80px;
  -webkit-animation: Animation8Shape4 1.5s ease infinite 1s;
  animation: Animation8Shape4 1.5s ease infinite 1s;
}

.jtyt-delivery-modal-tip-progress-bar .demoItem {
  background-color: rgba(0, 0, 0, 0.9);
}

.jtyt-delivery-modal-tip-progress-bar .shape-1 {
  background-color: #0576fd;
}

.jtyt-delivery-modal-tip-progress-bar .shape-2 {
  background-color: #ca6656;
}

.jtyt-delivery-modal-tip-progress-bar .shape-3 {
  background-color: #5bca6d;
}

.jtyt-delivery-modal-tip-progress-bar .shape-4 {
  background-color: #f2f236;
}

@keyframes Animation8Shape1 {
  0% {
    transfrom: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

@-webkit-keyframes Animation8Shape1 {
  0% {
    transfrom: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

@keyframes Animation8Shape2 {
  0% {
    transfrom: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

@-webkit-keyframes Animation8Shape2 {
  0% {
    transfrom: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

@keyframes Animation8Shape3 {
  0% {
    transfrom: scale(1);
    opacity: 0.5;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

@-webkit-keyframes Animation8Shape3 {
  0% {
    transfrom: scale(1);
    opacity: 0.5;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

@keyframes Animation8Shape4 {
  0% {
    transfrom: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7qq;
  }
}

@-webkit-keyframes Animation8Shape4 {
  0% {
    transfrom: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }

  100% {
    transfrom: scale(0.5);
    opacity: 0.7;
  }
}

.jtyt-delivery-modal-tip-txt {
  font-size: 20px;
  font-weight: bold;
  color: #eee;
  line-height: 26px;
}

.jtyt-delivery-modal-tip-btn {
  font-size: 14px;
  color: #fff;
  background: #439eff;
  padding: 8px 16px;
  border-radius: 5px;
  width: fit-content !important;
  transition: all 0.2s;
  cursor: pointer;
}

.jtyt-delivery-modal-tip-btn:hover {
  background: #0f78e7;
}

#jtyt-DeliveryModal-notice-alert {
  position: fixed;
  top: 0;
  right: 0;
  background-color: white;
  padding: 10px;
  z-index: 9999999;
  overflow: hidden;
  height: 330px;
  box-shadow: 0px 4px 12px #666;
  border-radius: 0px 0px 0px 8px;
  width: calc(50% - 200px);
  max-width: 450px;
  min-width: 300px;
  border-bottom: 3px solid rgb(67, 158, 255);
  border-left: 3px solid rgb(67, 158, 255);
}

.jtyt-DeliveryModal-title-bar {
  margin-bottom: 10px;
  border-bottom: 1px solid #ccc;
  display: flex;
  gap: 10px;
  padding: 4px 0 10px 0;
  flex-direction: column;
}

.jtyt-DeliveryModal-title {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 15px;
}

.jtyt-DeliveryModal-title2 {
  font-size: 14px;
  padding: 8px;
  background: #d8f8ff;
  border-radius: 5px;
}

.jtyt-loader1 {
  border: 2px solid #4393ff;
  border-top: 2px solid #fff;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  animation: spin 0.75s linear infinite;
}

.jtyt-DeliveryModal-title1 {
  font-size: 14px;
  display: flex;
  gap: 25px;
  padding: 0 8px;
}

.jtyt-DeliveryModal-content-bar-total,
.jtyt-DeliveryModal-content-bar-success,
.jtyt-DeliveryModal-content-bar-failed {
  font-size: 14px;
}

#jtyt-delivery-total {
  color: #1677ff;
  font-weight: bold;
  font-size: 16px;
}

#jtyt-delivery-success {
  color: #52c41a;
  font-weight: bold;
  font-size: 16px;
}

#jtyt-delivery-failed {
  color: #d4380d;
  font-weight: bold;
  font-size: 16px;
}

.jtyt-DeliveryModal-content {
  font-size: 14px;
  max-height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 152px;
}

.jtyt-DeliveryModal-btns {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 10px;
  border-top: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

#jtyt-DeliveryModal-btns-pause {
  padding: 5px 12px;
  background: #eee;
  color: #888;
  border-radius: 4px;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
}

#jtyt-DeliveryModal-btns-over {
  padding: 5px 12px;
  background: #f56c6c;
  color: #fff;
  border-radius: 4px;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
}

#jtyt-DeliveryModal-btns-pause:hover {
  opacity: 0.8;
}

#jtyt-DeliveryModal-btns-over:hover {
  opacity: 0.8;
}

.jtyt-DeliveryModal-content p {
  padding: 2px 0 !important;
  line-height: normal !important;
  text-align: initial !important;
}

.jtyt-DeliveryModal-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.jtyt-DeliveryModal-content::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 */
.jtyt-DeliveryModal-content::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

/* 鼠标悬停在滚动条滑块上 */
.jtyt-DeliveryModal-content::-webkit-scrollbar-thumb:hover {
  background: #c9c9c9;
  cursor: pointer;
}

.jtyt-cj-htmlDom {
  display: flex;
  flex-direction: column;
  gap: 13px;
  margin: 5px 0 10px 0;
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  padding: 10px;
}

.jtyt-cj-financialCenter-item-left {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  color: #111;
}

.jtyt-cj-financialCenter-item {
  border: 1px solid #ddd;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px;
  align-items: center;
}

.jtyt-cj-financialCenter-item-left-type {
  color: #439eff;
}

.jtyt-cj-financialCenter-item-left-timeRange {
  color: #dd1e1e;
  text-decoration: underline;
}

.jtyt-cj-financialCenter-item-right {
  display: flex;
  align-items: center;
}

.jtyt-cj-financialCenter-item-right-btns {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
}

.jtyt-cj-financialCenter-item-right-btns-download {
  border: 1px solid #439eff;
  background: #439eff;
  color: #fff;
  height: 30px;
  padding: 0 10px;
  border-radius: 6px;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.jtyt-cj-financialCenter-item-right-btns-download:hover {
  opacity: 0.8;
}

.jtyt-cj-financialCenter-item-left-exportTime {
  font-weight: 500;
}

.jtyt-cj-financialCenter-nodata {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  align-items: center;
}

.jtyt-exportHistory-tips {
  padding: 6px 8px;
  background: rgb(255, 247, 232);
  border-radius: 5px;
  font-size: 14px;
  margin: 10px;
  color: rgb(255, 125, 0);
  border: 1px solid rgba(255, 125, 0);
  line-height: 20px;
}

.jtyt-cj-financialCenter-text {
  color: #999;
}

.jtyt-cj-htmlDom::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: none;
}

.jtyt-cj-htmlDom::-webkit-scrollbar-track {
  background: transparent;
}

.jtyt-cj-htmlDom::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

.jtyt-cj-htmlDom::-webkit-scrollbar-thumb:hover {
  background: #c9c9c9;
  cursor: pointer;
}

#jtyt-cj-n-modal,
#jtyt-cj-modal-lookDetails
{
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .4);
  z-index: 99999999999999;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox {
  position: absolute;
  width: auto;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  display: block;
  border-radius: unset;
  inset: 0px;
  overflow: auto;
  background: #fff;
  font-size: 14px;
  padding: 0 15px 15px;
  transition: all .1s;
}

#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox {
  position: absolute;
  width: auto;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  display: block;
  border-radius: unset;
  inset: 0px;
  overflow: auto;
  background: #fff;
  font-size: 14px;
  padding: 0 15px 15px;
  transition: all .1s;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox #jtyt-cj-titlebar,
#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox #jtyt-cj-titlebar
{
  width: 100%;
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  font-weight: 700;
  font-size: 16px;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox #jtyt-cj-titlebar .btnArea,
#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox #jtyt-cj-titlebar .btnArea
{
  display: flex;
  align-items: center;
  gap: 15px;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox #jtyt-cj-titlebar #closebtn img,
#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox #jtyt-cj-titlebar #closebtn img 
{
  width: 20px;
  height: 20px;
  vertical-align: middle;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox #jtyt-cj-titlebar #closebtn,
#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox #jtyt-cj-titlebar #closebtn
{
  cursor: pointer;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox #jtyt-cj-titlebar #closebtn:hover,
#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox #jtyt-cj-titlebar #closebtn:hover
{
  color: #056eda;
}

#jtyt-cj-n-modal #jtyt-cj-n-modal-modalbox .modalcontent,
#jtyt-cj-modal-lookDetails #jtyt-cj-modal-lookDetails-modalbox .modalcontent 
{
  height: calc(100vh - 55px);
  width: 100%;
  color: #666;
  user-select: text;
}

#jtyt-cj-interval-refusePrice-modal {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999999999;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

#jtyt-cj-interval-refusePrice-titlebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.jtyt-cj-interval-refusePrice-tips {
  font-weight: bold;
  font-size: 18px;
}

#jtyt-cj-interval-refusePrice-modal #jtyt-cj-interval-refusePrice-modalbox {
  width: 965px;
  height: 680px;
  max-height: calc(100vh - 60px);
  background: linear-gradient(180deg,
      rgb(215 237 249),
      rgb(255 255 255),
      rgb(255 255 255),
      rgb(255 255 255),
      rgb(255 255 255),
      rgb(255 255 255),
      rgb(255 255 255),
      rgb(255 255 255),
      rgb(255 255 255));
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
  box-shadow: 0 0 22px rgba(0, 0, 0, 0.25);
  padding: 15px;
  transition: all 0.2s;
}

#jtyt-interval-refusePrice-type {
  display: flex;
  align-items: center;
  height: 26px;
}

.jtyt-interval-type-radio {
  display: flex;
  gap: 20px;
  align-items: center;
}

#jtyt-interval-refusePrice-timeSlots {
  display: flex;
  justify-content: space-between;
}

.jtyt-interval-refusePrice-contaniner {
  display: flex;
  gap: 15px;
  overflow-y: auto;
}

.jtyt-interval-refusePrice-contaniner-left {
  width: 240px;
  padding: 10px;
  border: 1px solid #00000020;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background: #439eff05;
  border-radius: 6px;
}

.jtyt-interval-refusePrice-contaniner-right {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 50px);
  display: flex;
  flex-direction: column;
  gap: 15px;
  justify-content: space-between;
  padding-bottom: 50px;
}

.jtyt-interval-refusePrice-contaniner-left-title {
  font-size: 16px;
  font-weight: bold;
  padding: 4px 10px 0px 7px;
  color: #1c3586;
}

.jtyt-interval-refusePrice-contaniner-left-content-item {
  color: #439eff;
  font-size: 14px;
  line-height: 18px;
  padding: 6px 10px;
  border-radius: 4px;
  word-break: break-all;
  background: #439eff10;
}

.jtyt-interval-refusePrice-contaniner {
  display: flex;
  gap: 15px;
}

.jtyt-interval-refusePrice-contaniner-left-content {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 4px;
}

/* 滚动条宽度 */
.jtyt-interval-refusePrice-contaniner::-webkit-scrollbar,
.jtyt-interval-refusePrice-contaniner-left-content::-webkit-scrollbar,
.jtyt-interval-refusePrice-contaniner-right::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 滚动条轨道 */
.jtyt-interval-refusePrice-contaniner::-webkit-scrollbar-track,
.jtyt-interval-refusePrice-contaniner-left-content::-webkit-scrollbar-track,
.jtyt-interval-refusePrice-contaniner-right::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 */
.jtyt-interval-refusePrice-contaniner-left-content .jtyt-interval-refusePrice-contaniner::-webkit-scrollbar-thumb,
.jtyt-interval-refusePrice-contaniner-left-content::-webkit-scrollbar-thumb,
.jtyt-interval-refusePrice-contaniner-right::-webkit-scrollbar-thumb {
  background: #eceaea;
}

/* 鼠标悬停在滚动条滑块上 */
.jtyt-interval-refusePrice-contaniner::-webkit-scrollbar-thumb:hover,
.jtyt-interval-refusePrice-contaniner-left-content::-webkit-scrollbar-thumb:hover,
.jtyt-interval-refusePrice-contaniner-right::-webkit-scrollbar-thumb:hover {
  background: #c7c7c7;
  cursor: pointer;
}

#jtyt-cj-interval-refusePrice-modal #jtyt-cj-interval-refusePrice-modalbox .jtyt-interval-refusePrice-titlebar {
  width: 100%;
  min-height: 40px;
  max-height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
}

#jtyt-cj-interval-refusePrice-modal #jtyt-cj-interval-refusePrice-modalbox #jtyt-cj-interval-refusePrice-titlebar .jtyt-cj-interval-refusePrice-btnArea {
  display: flex;
  align-items: center;
  gap: 15px;
}

#jtyt-cj-interval-refusePrice-modal #jtyt-cj-interval-refusePrice-modalbox #jtyt-cj-interval-refusePrice-titlebar #jtyt-cj-interval-refusePrice-closebtn {
  cursor: pointer;
}

#jtyt-cj-interval-refusePrice-modal #jtyt-cj-interval-refusePrice-modalbox #jtyt-cj-interval-refusePrice-titlebar #jtyt-cj-interval-refusePrice-closebtn:hover {
  color: #555;
  background: #eee;
}

#jtyt-cj-interval-refusePrice-modal #jtyt-cj-interval-refusePrice-modalbox #jtyt-cj-interval-refusePrice-titlebar #jtyt-cj-interval-refusePrice-closebtn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border-radius: 3px;
}

.jtyt-interval-refusePrice-btns button:disabled {
  opacity: 0.4;
}

.jtyt-interval-refusePrice-btns button:hover {
  opacity: 0.8;
}

#jtyt-interval-refusePrice-saveTimes {
  border: none;
  background: #439eff;
  color: #fff;
  border-radius: 3px;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
  height: 26px;
}

#jtyt-interval-refusePrice-clearTimes {
  border: none;
  background: #f56c6c;
  color: #fff;
  border-radius: 3px;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
  height: 26px;
}

#jtyt-interval-refusePrice-startTask {
  border: none;
  background: #67c23a;
  color: #fff;
  border-radius: 3px;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
  height: 26px;
}

#jtyt-interval-refusePrice-stopButton {
  border: none;
  background: #909399;
  color: #fff;
  border-radius: 3px;
  transition: all 0.2s;
  font-size: 14px;
  cursor: pointer;
  height: 26px;
}

#jtyt-interval-refusePrice-openAndStop {
  border: 1px solid #999;
  background: #fff;
  color: #333;
  border-radius: 3px;
  transition: all 0.2s;
  font-size: 14px;
  cursor: pointer;
  height: 26px;
}

#jtyt-interval-refusePrice-timeSlots input[type="time"]::-webkit-calendar-picker-indicator {
  -webkit-appearance: none;
}

#jtyt-interval-refusePrice-timeSlots input[type="time"] {
  padding: 0 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  color: #444;
  height: 24.5px;
}

#jtyt-interval-refusePrice-timeSlots input[type="time"]:focus {
  border-color: #439eff;
  box-shadow: 0 0 3px rgba(74, 144, 226, 0.5);
  outline: none;
}

#jtyt-interval-refusePrice-timeSlots input[type="time"]:hover {
  border-color: #439eff;
  cursor: pointer;
}

#jtyt-interval-refusePrice-timeSlots label {
  font-size: 14px;
}

#jtyt-interval-refusePrice-taskStatus {
  height: 40px;
  min-height: 40px;
  padding: 0px 10px;
  border: 1px solid #439eff;
  border-radius: 4px;
  font-size: 16px;
  background: #e9f7ff;
  color: #333;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 6px;
}

#jtyt-interval-refusePrice-taskStatus span {
  display: flex;
}

.jtyt-interval-refusePrice-progress-bar {
  background-color: #d1d5db;
  border-radius: 9999px;
  width: 200px;
  height: 0.6rem;
  position: relative;
  overflow: hidden;
}

.jtyt-interval-refusePrice-progress-bar-progress {
  background-color: #3b82f6;
  border-radius: 9999px;
  position: absolute;
  bottom: 0;
  top: 0;
  width: 50%;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-name: progress-bar;
}

@keyframes progress-bar {
  from {
    left: -50%;
  }

  to {
    left: 100%;
  }
}

.jtyt-interval-refusePrice-btns {
  margin-top: 35px;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
  padding: 10px;
  border-radius: 6px;
  background: #fff;
  position: absolute;
  bottom: 10px;
  box-shadow: 0 0 10px #00000020;
}

.jtyt-interval-refusePrice-title {
  font-size: 14px;
  color: #f56c6c;
  line-height: normal !important;
  padding: 10px;
  border: 1px solid #00000020;
  border-radius: 6px;
  display: flex;
  background: #439eff05;
  flex-direction: column;
  gap: 5px;
}

#jtyt-interval-refusePrice-phoneNumber-input {
  outline: 0;
  border: 1px solid #ccc;
  height: 24.5px;
  border-radius: 4px;
  width: 117.5px;
  padding: 0 8px;
}

#jtyt-interval-refusePrice-phoneNumber div input[type="text"]:focus {
  border-color: #439eff;
  box-shadow: 0 0 3px rgba(74, 144, 226, 0.5);
  outline: none;
}

#jtyt-interval-refusePrice-phoneNumber div input[type="text"]:hover {
  border-color: #439eff;
  cursor: pointer;
}

#jtyt-interval-refusePrice-phoneNumber div label {
  font-size: 14px;
}

#jtyt-interval-refusePrice-phoneNumber {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

#jtyt-interval-refusePrice-tips {
  color: #f56c6c;
}

#jtyt-interval-mallName {
  display: flex;
}

.jtyt-interval-mallName-checkbox {
  width: calc(100% - 70px);
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 5px;
}

.jtyt-interval-mallName-checkbox-items {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 5px 15px;
  height: 45px;
  overflow: auto;
  flex-direction: column;
}

.zoomIn1 {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

.zoomOut1 {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut;
}

.fadeIn1 {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

.fadeOut1 {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

/* 滚动条宽度 */
.jtyt-interval-mallName-checkbox-items::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滚动条轨道 */
.jtyt-interval-mallName-checkbox-items::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 */
.jtyt-interval-mallName-checkbox-items::-webkit-scrollbar-thumb {
  background: #eceaea;
  border-radius: 3px;
}

/* 鼠标悬停在滚动条滑块上 */
.jtyt-interval-mallName-checkbox-items::-webkit-scrollbar-thumb:hover {
  background: #c7c7c7;
  cursor: pointer;
}

#jtyt-collect-loading {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
  justify-content: center;
  z-index: 999999999;
}

#jtyt-collect-loading span {
  font-size: 16px;
  color: #333;
}

#jtyt-collect-loading #box {
  padding: 20px 0px;
  width: 400px;
  min-width: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 5px 22px #666;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

#jtyt-collect-loading #box img {
  width: 32px;
  height: 32px;
  display: none;
}

#jtyt-collect-loading #progressBar {
  width: 300px;
  margin-top: 15px;
  height: 18px;
  border-radius: 10px;
  background-color: #cdcdcd;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

#jtyt-collect-loading #progress {
  height: 18px;
  background-color: #439eff;
  position: absolute;
  border-radius: 10px;
  left: 0;
  width: 0%;
  transition: width 0.5s ease-in-out;
  z-index: 9999999;
}

#jtyt-collect-loading span#text {
  font-size: 12px;
  color: #fff;
  z-index: 99999999;
}

#jtyt-collect-loading .container {
  position: relative;
  padding: 20px;
  display: flex;
  gap: 10px;
}

#jtyt-collect-loading #btns {
  position: relative;
  justify-content: center;
  gap: 20px;
  align-items: center;
  display: none;
}

#jtyt-collect-loading #btns #confirm {
  padding: 8px 22px;
  background: #439eff;
  color: #fff;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer;
}

#jtyt-collect-loading #btns #confirm:hover {
  opacity: 0.8;
}

#jtyt-collect-loading #btns #cancel:hover {
  background: #eee;
}

#jtyt-collect-loading #btns #cancel {
  padding: 8px 22px;
  background: transparent;
  color: #333;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer;
}

#jtyt-collect-loading .text__box {
  position: relative;
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 20px;
}

#jtyt-collect-loading .one {
  animation-delay: 0s;
}

@keyframes width {
  0% {
    width: 0;
  }

  100% {
    width: 22em;
  }
}

@keyframes showInfinite {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

#jtyt-update-notice-alert {
  position: fixed;
  width: 350px;
  top: 20px;
  right: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px #ccc;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 9999999999;
  overflow: hidden;
}

#jtyt-update-notice-alert .jtyt-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#jtyt-update-notice-alert .jtyt-title-bar .jtyt-text {
  font-size: 18px;
  font-weight: bold;
}

#jtyt-update-notice-alert .jtyt-title-bar .jtyt-close {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  color: #999;
  transition: all 0.2s;
}

#jtyt-update-notice-alert .jtyt-title-bar .jtyt-close:hover {
  color: #333;
}

#jtyt-update-notice-alert .jtyt-content {
  font-size: 14px;
  color: #222;
  height: 250px;
  overflow-y: auto;
  padding: 0 0 10px 0;
}

#jtyt-update-notice-alert .jtyt-tips-bar1 {
  padding: 8px 10px;
  background: #bcf74c38;
  border: 1px solid #bcf74c;
  font-size: 14px;
  color: #333;
  margin: 5px 0;
  border-radius: 5px;
  line-height: normal !important;
  text-align: initial !important;
}

#jtyt-update-notice-alert .jtyt-tips-bar2 {
  padding: 8px 10px;
  background: #f56c6c10;
  border: 1px solid #f56c6c;
  font-size: 14px;
  color: #f56c6c;
  margin: 5px 0;
  border-radius: 5px;
  line-height: normal !important;
  text-align: initial !important;
}

#jtyt-update-notice-alert .jtyt-content p {
  padding: 2px 0 !important;
  line-height: normal !important;
  text-align: initial !important;
}

/* 滚动条宽度 */
#jtyt-update-notice-alert .jtyt-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滚动条轨道 */
#jtyt-update-notice-alert .jtyt-content::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 */
#jtyt-update-notice-alert .jtyt-content::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

/* 鼠标悬停在滚动条滑块上 */
#jtyt-update-notice-alert .jtyt-content::-webkit-scrollbar-thumb:hover {
  background: #c9c9c9;
  cursor: pointer;
}

#jtyt-update-notice-alert .jt-btn {
  padding: 6px 10px;
  background: #439eff;
  color: #fff;
  font-size: 14px;
  border-radius: 5px;
  transition: all 0.3s;
  text-align: center;
  cursor: pointer;
  margin-top: 3px;
}

#jtyt-update-notice-alert .jt-btn-ignore {
  background: #eee !important;
  color: #888 !important;
  padding: 6px 10px;
  font-size: 14px;
  border-radius: 5px;
  transition: all 0.3s;
  text-align: center;
  cursor: pointer;
}

#jtyt-update-notice-alert .jt-btn:hover,
#jtyt-update-notice-alert .jt-btn-ignore:hover {
  opacity: 0.8;
}

@-webkit-keyframes bounceInRight {

  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }

  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }

  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes bounceInRight {

  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }

  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }

  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight;
}

#jtyt-cj-cjUpateStatement-modal {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 99999999999999;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox {
  width: calc(100% - 300px);
  max-width: 1200px;
  height: calc(100vh - 200px);
  overflow: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
  box-shadow: 0 0 22px rgba(0, 0, 0, 0.25);
  padding: 5px 15px 15px;
  transition: all 0.2s;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox #jtyt-cj-cjUpateStatement-titlebar {
  width: 100%;
  min-height: 40px;
  max-height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox #jtyt-cj-cjUpateStatement-titlebar .jtyt-cj-cjUpateStatement-btnArea {
  display: flex;
  align-items: center;
  gap: 15px;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox #jtyt-cj-cjUpateStatement-titlebar #jtyt-cj-cjUpateStatement-closebtn {
  cursor: pointer;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox #jtyt-cj-cjUpateStatement-titlebar #jtyt-cj-cjUpateStatement-closebtn:hover {
  color: #555;
  background: #eee;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox #jtyt-cj-cjUpateStatement-titlebar #jtyt-cj-cjUpateStatement-closebtn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border-radius: 3px;
}

#jtyt-cj-cjUpateStatement-modal #jtyt-cj-cjUpateStatement-modalbox .jtyt-cj-cjUpateStatement-modalcontent {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: calc(100vh - 270px);
  gap: 50px;
}

.jtyt-cj-cjUpateStatement-modalcontent-tips {
  color: #333;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  width: 100%;
  font-weight: bold;
  font-size: 18px;
}

.jtyt-cj-cjUpateStatement-modalcontent-tips>div {
  width: 50%;
}

.jtyt-cj-cjUpateStatement-modalcontent-tips img {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.jtyt-cj-cjUpateStatement-btns {
  display: flex !important;
  justify-content: center !important;
  gap: 10px !important;
  align-items: center !important;
}

.jtyt-cj-cjUpateStatement-btns-confirm {
  padding: 4px 20px;
  border-radius: 4px;
  cursor: pointer;
  background: #439eff;
  color: #fff;
  border: 1px solid #439eff;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.jtyt-cj-cjUpateStatement-btns-confirm {
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  animation: spin 1s linear infinite;
  display: none;
}

.jtyt-cj-cjUpateStatement-btns-cancel {
  padding: 4px 20px;
  border-radius: 4px;
  border: 1px solid #999;
  cursor: pointer;
  transition: all 0.2s;
}

.jtyt-cj-cjUpateStatement-btns-cancel:hover,
.jtyt-cj-cjUpateStatement-btns-confirm:hover {
  opacity: 0.8;
}

.jtyt-cj-cjUpateStatement-tips {
  color: #333;
  font-size: 16px;
}

/* 滚动条宽度 */
.jtyt-cj-cjUpateStatement-modalcontent::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: none;
}

/* 滚动条轨道 */
.jtyt-cj-cjUpateStatement-modalcontent::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 */
.jtyt-cj-cjUpateStatement-modalcontent::-webkit-scrollbar-thumb {
  background: #eceaea;
  border-radius: 4px;
}

/* 鼠标悬停在滚动条滑块上 */
.jtyt-cj-cjUpateStatement-modalcontent::-webkit-scrollbar-thumb:hover {
  background: #c7c7c7;
  cursor: pointer;
}