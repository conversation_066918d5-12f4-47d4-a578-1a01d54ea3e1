/* 装饰 */
.jtyt-btn {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.jtyt-btn-decorate {
  position: absolute;
  background-color: #439eff;
  top: 0;
  left: 0;
  color: #fff;
  font-size: 10px;
  border-bottom-right-radius: 7px;
  padding: 1px 4px;
}

/* 校验按钮 */
.jtyt-cj-verifyCanList-putaway {
  border: 1px solid #439eff;
  background-color: #fff;
  color: #439eff;
  position: fixed;
  z-index: 333;
  height: 40px;
  border-radius: 4px;
  width: 150px;
  padding: 0 !important;
}
.jtyt-cj-verifyCanList-putaway:hover {
  opacity: 0.8;
}
.jtyt-cj-verifyCanList-putaway .jtyt-loader {
  border: 1.5px solid transparent;
  border-top: 1.5px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
  display: none;
}
.jtyt-cj-verifyCanList-update {
  width: 125px;
}
#jtyt-cj-vip {
  width: 24px;
  height: 14px;
  position: absolute;
  top: -7px;
  right: -12px;
}
/* 模板弹窗 */
.jtyt-verifyCanList-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.jtyt-verifyCanList-content-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: flex-start;
  gap: 6px;
}
.jtyt-verifyCanList-content-header {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  width: 100%;
  color: #555;
}

.jtyt-verifyCanList-modal-body #templateIfame {
  width: 800px;
  height: 500px;
  border-width: 0px;
  overflow: hidden;
}
/* 上传校验 */
.validate-contet {
  width: 500px;
}
.jt-putaway-contet {
  width: 600px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
}
.jt-putaway-twig {
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  flex-wrap: nowrap;
  gap: 25px;
  height: fit-content;
}

.jt-putaway-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: start;
}

.jt-putaway-error-info {
  padding: 5px 8px;
  color: rgb(245, 63, 63);
  background-color: #ffece8;
  margin-top: 5px;
  text-align: start;
  border-radius: 4px;
}

.jt-putaway-schedule-info {
  padding: 2px 4px;
  color: #459cff;
  border-color: #459cff;
  margin-top: 3px;
  font-size: 10px;
  text-align: start;
}

.jt-putaway-tag {
  font-size: 13px;
  white-space: nowrap;
  padding: 3px 5px;
  border: 1px solid;
  border-radius: 4px;
}

.jt-putaway-primary {
  color: rgb(22, 93, 255);
  border-color: rgb(22, 93, 255);
}

.jt-putaway-error {
  color: rgb(245, 63, 63);
  border-color: rgb(245, 63, 63);
  background-color: #ffece8;
  cursor: pointer;
}

.jt-putaway-succee {
  color: rgb(0, 180, 42);
  border-color: rgb(0, 180, 42);
  background-color: #e8ffea;
}

.jt-putaway-all-error {
  padding: 7px 0px;
  background-color: #e82121;
  color: #fff;
  border-radius: 5px;
  border: 1px;
  border-style: dashed;
  cursor: pointer;
}

.validate-hint {
  color: #e82121;
}