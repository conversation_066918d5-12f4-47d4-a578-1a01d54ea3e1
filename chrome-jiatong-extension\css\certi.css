/* 导出数据按钮 */
.jtyt-cj-certi {
  background-color: #449fff;
  position: relative;
  color: #fff;
  z-index: 333;
  height: auto;
  border-radius: 4px;
  width: fit-content;
  padding: 3px 7px;
  font-size: 12px;
  cursor: pointer;
}
.jtyt-cj-certi:hover {
  opacity: 0.8;
}
.jtyt-cj-certi .jtyt-loader {
  border: 1.5px solid transparent;
  border-top: 1.5px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
  display: none;
}

.jtyt-cj-certi-save {
  background-color: #449fff;
  position: relative;
  color: #fff;
  z-index: 333;
  height: auto;
  border-radius: 4px;
  width: fit-content;
  margin-left: 10px;
  padding: 3px 7px;
  font-size: 12px;
  cursor: pointer;
}
.jtyt-cj-certi-save:hover {
  opacity: 0.8;
}
.jtyt-cj-certi-save .jtyt-loader {
  border: 1.5px solid transparent;
  border-top: 1.5px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
  display: none;
}

.jt-certi-content-save {
  display: flex;
  width: 400px;
  align-items: center;
  white-space: nowrap;
}

.jt-certi-content-save input {
  outline: none;
  width: 100%;
  border: 1px solid #a9acb3;
  border-radius: 3px;
}

#certiIframe {
  width: 600px;
  height: 450px;
  border: 0;
}

.jt-certi-modal-overlay  .jt-certi-modal-body {
  margin: 0;
}

.jt-certi-checkbox {
  margin-right: 5px;
}

.indeterminate {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.indeterminate::after {
  content: "\f068"; /* 减号图标 */
  font-family: "FontAwesome";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
}
