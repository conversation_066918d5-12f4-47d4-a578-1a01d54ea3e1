#jtyt-cj-box-temu {
  position: fixed;
  bottom: 10px;
  right: 20px;
  z-index: 9999999;
  border-radius: 8px;
  box-shadow: 0 0 20px #00000030;
  background: #fff;
  padding: 6px;
  transition: all .3s;
}

#jtyt-cj-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 6px;
  z-index: 9999;
}

#jtyt-cj-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

#jtyt-cj-logo img {
  width: 21px;
  display: block;
  cursor: pointer;
}

#jtyt-cj-text {
  font-size: 14px;
  line-height: 21px;
  color: #666;
}

#jtyt-cj-btns {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  margin-top: 7px;
}

.jt-cj-button-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  gap: 15px;
  z-index: 1000;
}

.jt-cj-action-button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  transition: all 0.25s ease;
}

.jt-cj-btn-all-features {
  border: 1px solid #E6A23C;
  color: #E6A23C;
  background: transparent;
}

.jt-cj-btn-quick-features {
  background-color: #439eff;
  border: 1px solid #439eff;
  color: white;
}

.jt-cj-action-button:hover {
  opacity: .8;
  box-shadow: 0 4px 13px rgba(0, 0, 0, 0.2);
}

.jt-cj-drawer {
  position: fixed;
  bottom: -100%;
  left: 0;
  width: 100%;
  height: calc(100vh - 60px);
  background-color: white;
  box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  transition: bottom 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
  z-index: 99999999;
}

.jt-cj-drawer.open {
  bottom: 0;
}

.jt-cj-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.jt-cj-drawer-title {
  font-size: 16px;
  font-weight: bold;
}

.jt-cj-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.jt-cj-close-btn:hover {
  color: #333;
}

.jt-cj-drawer-content {
  height: calc(100vh - 138px);
  overflow-y: auto;
  padding: 10px;
}

.jt-cj-quick-feature-list {
  position: absolute;
  bottom: 40px;
  right: 0;
  width: -webkit-fill-available !important;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1500;
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s;
  transform: translateY(30px);
  opacity: 0;
  overflow: hidden;
  pointer-events: none;
}

.jt-cj-quick-feature-list.open {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

.jt-cj-quick-feature-item {
  padding: 7px 11px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.jt-cj-feature-icon {
  display: flex;
}

.jt-cj-quick-feature-item:last-child {
  border-bottom: none;
}

.jt-cj-quick-feature-item:hover {
  background-color: #f5f5f5;
}

.jt-cj-feature-icon {
  margin-right: 8px;
  font-size: 14px;
}

.jt-cj-feature-text {
  font-size: 14px;
  color: #333 !important;
}

#jtyt-cj-close {
  cursor: pointer;
  transform: rotate(90deg);
}

.jtyt-cj-page-btns,
.jtyt-cj-page-btns-row {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  background: linear-gradient(325deg, rgb(230, 243, 255), white);
  align-items: flex-start;
  border: 1px solid #439EFF;
  margin: 10px 0;
}

.jtyt-cj-page-btns-row {
  flex-direction: row;
  align-items: center;
}

.jtyt-cj-page-btns .jtyt-cj-btnsArea,
.jtyt-cj-page-btns-row .jtyt-cj-btnsArea {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.jtyt-cj-page-btns .jtyt-cj-logo,
.jtyt-cj-page-btns-row .jtyt-cj-logo {
  height: 20px;
  display: flex;
}

.jtyt-cj-page-btns .jtyt-cj-logo img,
.jtyt-cj-page-btns-row .jtyt-cj-logo img {
  width: 90px;
}

.jtyt-cj-page-btns .jtyt-cj-btnsArea>div,
.jtyt-cj-page-btns-row .jtyt-cj-btnsArea>div {
  width: fit-content !important;
  margin-right: 10px;
  height: 28px;
  text-align: center;
  border: 1px solid #439eff;
  border-radius: 6px;
  font-size: 14px;
  color: #fff;
  padding: 0 10px;
  cursor: pointer;
  background: #439eff;
  box-sizing: border-box;
  transition: all .2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  position: relative;
}

.jtyt-cj-page-btns .jtyt-cj-btnsArea>div:hover,
.jtyt-cj-page-btns-row .jtyt-cj-btnsArea>div:hover {
  opacity: .8;
}

.jtyt-cj-page-btns .jtyt-cj-btnsArea>div .jtyt-loader,
.jtyt-cj-page-btns-row .jtyt-cj-btnsArea>div .jtyt-loader {
  border: 1.5px solid transparent;
  border-top: 1.5px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
  display: none;
}

.jtyt-EditCompliances-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.jtyt-EditCompliances-content-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: flex-start;
  gap: 3px;
}

.jtyt-EditCompliances-content-item span {
  font-size: 14px;
  color: #555;
}

.jtyt-EditCompliances-content-item input {
  width: 650px;
  height: 34px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 14px;
  color: #555;
  outline: none;
  box-sizing: border-box;
  transition: all 0.2s;
}

.jtyt-EditCompliances-content-item input:hover {
  border-color: #439eff;
}

.jtyt-EditCompliances-content-item input:focus {
  border-color: #439eff;
  outline: none;
}

.jtyt-EditCompliances-content-item input::placeholder {
  color: #999;
  font-size: 14px;
}