/* 导出数据按钮 */
.jtyt-cj-dispatch {
  background-color: #449fff;
  position: relative;
  color: #fff;
  z-index: 333;
  height: auto;
  border-radius: 4px;
  width: auto;
  padding: 3px 7px;
  font-size: 15px;
  cursor: pointer;
  transition: all .2s;
}
.jtyt-cj-dispatch:hover {
  opacity: 0.8;
}
.jtyt-cj-dispatch .jtyt-loader {
  border: 1.5px solid transparent;
  border-top: 1.5px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
  display: none;
}

#jtyt-cj-vip {
  width: 24px;
  height: 14px;
  position: absolute;
  top: -7px;
  right: -12px;
}

#timeIfame {
  width: 550px;
  height: 380px;
  border: 0;
}

.jt-dispatch-modal-overlay .jt-dispatch-modal-body {
  margin-bottom: 0px !important;
}
